import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  Image,
  StatusBar,
  Alert
} from 'react-native';
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'expo-router';
import BottomNavigation from './components/BottomNavigation';
import { getAIResponse, isCrisisMessage } from './services/openaiService';
import { AI_CONFIG } from './config/aiConfig';

const AIChatbot = () => {
  const router = useRouter();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    {
      id: '1',
      sender: 'bot',
      text: 'Hi! I\'m <PERSON><PERSON>, your AI Mental Health Assistant. I\'m here to provide support, listen to your concerns, and help you with coping strategies. How are you feeling today?'
    }
  ]);
  const [isTyping, setIsTyping] = useState(false);
  const [activeTab, setActiveTab] = useState('feeling');

  const flatListRef = useRef(null);

  // Quick reply options
  const quickReplies = {
    feeling: [
      { id: 'f1', text: 'I need someone to talk to...' },
      { id: 'f2', text: 'Feeling overwhelmed today...' },
      { id: 'f3', text: 'Help me relax' },
      { id: 'f4', text: 'Need meditation...' }
    ],
    thoughts: [
      { id: 't1', text: 'I can\'t stop worrying' },
      { id: 't2', text: 'My mind is racing' },
      { id: 't3', text: 'I feel stuck' },
      { id: 't4', text: 'I need clarity' }
    ],
    support: [
      { id: 's1', text: 'I need professional help' },
      { id: 's2', text: 'How can I feel better?' },
      { id: 's3', text: 'Coping strategies' },
      { id: 's4', text: 'Resources for mental health' }
    ]
  };

  // Get conversation history for context
  const getConversationHistory = () => {
    return messages.slice(-AI_CONFIG.maxHistoryMessages);
  };

  const handleSend = async () => {
    if (message.trim() === '') return;

    const userMessageText = message.trim();

    // Check for crisis keywords
    if (isCrisisMessage(userMessageText)) {
      Alert.alert(
        "Crisis Support",
        "I'm concerned about you. Please consider reaching out to a mental health professional or crisis helpline immediately. In the US, you can call 988 for the Suicide & Crisis Lifeline.",
        [
          { text: "OK", style: "default" },
          { text: "Call 988", onPress: () => {/* In a real app, you'd implement calling functionality */} }
        ]
      );
    }

    const newUserMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: userMessageText
    };

    setMessages(prevMessages => [...prevMessages, newUserMessage]);
    setMessage('');
    setIsTyping(true);

    try {
      // Get conversation history for context
      const history = getConversationHistory();

      // Get AI response
      const aiResponse = await getAIResponse(userMessageText, history);

      const botResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: aiResponse
      };

      setMessages(prevMessages => [...prevMessages, botResponse]);
    } catch (error) {
      console.error('Error getting AI response:', error);

      // Fallback response
      const fallbackResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: "I'm having trouble connecting right now, but I'm here for you. Try taking a few deep breaths and remember that you're not alone."
      };

      setMessages(prevMessages => [...prevMessages, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickReply = async (reply) => {
    // Check for crisis keywords
    if (isCrisisMessage(reply.text)) {
      Alert.alert(
        "Crisis Support",
        "I'm concerned about you. Please consider reaching out to a mental health professional or crisis helpline immediately. In the US, you can call 988 for the Suicide & Crisis Lifeline.",
        [
          { text: "OK", style: "default" },
          { text: "Call 988", onPress: () => {/* In a real app, you'd implement calling functionality */} }
        ]
      );
    }

    const newUserMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: reply.text
    };

    setMessages(prevMessages => [...prevMessages, newUserMessage]);
    setIsTyping(true);

    try {
      // Get conversation history for context
      const history = getConversationHistory();

      // Get AI response
      const aiResponse = await getAIResponse(reply.text, history);

      const botResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: aiResponse
      };

      setMessages(prevMessages => [...prevMessages, botResponse]);
    } catch (error) {
      console.error('Error getting AI response:', error);

      // Fallback response
      const fallbackResponse = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        text: "I'm having trouble connecting right now, but I'm here for you. Try taking a few deep breaths and remember that you're not alone."
      };

      setMessages(prevMessages => [...prevMessages, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  const renderMessage = ({ item }) => {
    const isBot = item.sender === 'bot';

    return (
      <View style={[
        styles.messageBubble,
        isBot ? styles.botBubble : styles.userBubble
      ]}>
        {isBot && (
          <View style={styles.botAvatarContainer}>
            <View style={styles.botAvatar}>
              <Text style={styles.botAvatarText}>AI</Text>
            </View>
          </View>
        )}
        <View style={[
          styles.messageContent,
          isBot ? styles.botContent : styles.userContent
        ]}>
          <Text style={[
            styles.messageText,
            isBot ? styles.botText : styles.userText
          ]}>
            {item.text}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === "ios" ? 90 : 0}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <View style={styles.headerTitleContainer}>
            <View style={styles.headerLogoContainer}>
              <Image
                source={require('../assets/logo mentalease.png')}
                style={styles.headerLogo}
                resizeMode="contain"
              />
              <Text style={styles.headerBrand}>Mentalease</Text>
            </View>
            <Text style={styles.headerTitle}>AIRA</Text>
            <Text style={styles.headerSubtitle}>AI Mental Health Assistant</Text>
          </View>
          <View style={styles.statusIndicator} />
        </View>

        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.messagesList}
        />

        {isTyping && (
          <View style={styles.typingIndicator}>
            <Text style={styles.typingText}>AIRA is typing...</Text>
          </View>
        )}

        <View style={styles.quickRepliesContainer}>
          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'feeling' && styles.activeTab]}
              onPress={() => setActiveTab('feeling')}
            >
              <Text style={[styles.tabText, activeTab === 'feeling' && styles.activeTabText]}>
                💭 Feeling
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'thoughts' && styles.activeTab]}
              onPress={() => setActiveTab('thoughts')}
            >
              <Text style={[styles.tabText, activeTab === 'thoughts' && styles.activeTabText]}>
                💡 Thoughts
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'support' && styles.activeTab]}
              onPress={() => setActiveTab('support')}
            >
              <Text style={[styles.tabText, activeTab === 'support' && styles.activeTabText]}>
                💪 Support
              </Text>
            </TouchableOpacity>
          </View>

          <FlatList
            horizontal
            data={quickReplies[activeTab]}
            keyExtractor={item => item.id}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.quickRepliesList}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.quickReplyButton}
                onPress={() => handleQuickReply(item)}
              >
                <Text style={styles.quickReplyText}>{item.text}</Text>
              </TouchableOpacity>
            )}
          />
        </View>

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Type your message..."
            value={message}
            onChangeText={setMessage}
            multiline
            maxHeight={100}
          />
          <TouchableOpacity
            style={styles.sendButton}
            onPress={handleSend}
            disabled={message.trim() === ''}
          >
            <View style={[
              styles.sendButtonCircle,
              message.trim() === '' ? styles.sendButtonDisabled : {}
            ]}>
              <Text style={styles.sendButtonText}>→</Text>
            </View>
          </TouchableOpacity>
        </View>
        <StatusBar backgroundColor="#F5F9EE" barStyle="dark-content" />
        <BottomNavigation />
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default AIChatbot;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  headerLogoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  headerLogo: {
    width: 20,
    height: 20,
    marginRight: 6,
  },
  headerBrand: {
    color: '#6B9142',
    fontSize: 12,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: '#6B9142',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: '#6B9142',
    fontSize: 12,
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#6B9142',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 1,
  },
  messagesList: {
    padding: 15,
    paddingBottom: 80, // Space for bottom navigation
  },
  messageBubble: {
    flexDirection: 'row',
    marginBottom: 15,
    maxWidth: '80%',
  },
  botBubble: {
    alignSelf: 'flex-start',
  },
  userBubble: {
    alignSelf: 'flex-end',
    flexDirection: 'row-reverse',
  },
  botAvatarContainer: {
    marginRight: 8,
    alignSelf: 'flex-end',
  },
  botAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#8FB565',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  botAvatarText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  messageContent: {
    borderRadius: 18,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  botContent: {
    backgroundColor: '#F5F9EE',
    borderBottomLeftRadius: 5,
  },
  userContent: {
    backgroundColor: '#6B9142',
    borderBottomRightRadius: 5,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  botText: {
    color: '#333333',
  },
  userText: {
    color: '#FFFFFF',
  },
  typingIndicator: {
    padding: 10,
    alignItems: 'flex-start',
    marginLeft: 15,
  },
  typingText: {
    color: '#999999',
    fontSize: 12,
    fontStyle: 'italic',
  },
  quickRepliesContainer: {
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#8FB565',
  },
  tabText: {
    color: '#999999',
    fontSize: 12,
  },
  activeTabText: {
    color: '#8FB565',
    fontWeight: 'bold',
  },
  quickRepliesList: {
    padding: 10,
  },
  quickReplyButton: {
    backgroundColor: '#F5F9EE',
    borderRadius: 18,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  quickReplyText: {
    color: '#666666',
    fontSize: 12,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    backgroundColor: '#FFFFFF',
    marginBottom: 60, // Space for bottom navigation
  },
  input: {
    flex: 1,
    backgroundColor: '#F5F9EE',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    maxHeight: 100,
    fontSize: 14,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  sendButton: {
    marginLeft: 10,
  },
  sendButtonCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#6B9142',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
