import { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Image,
  Animated,
  Easing,
  Platform,
  Dimensions,
  StatusBar
} from 'react-native';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import CustomStatusBar from './components/CustomStatusBar';
import HeaderCarousel from './components/HeaderCarousel';

// Inspirational quotes for the dashboard
const inspirationalQuotes = [
  "Mental health is not a destination, but a journey.",
  "You don't have to be positive all the time. It's perfectly okay to feel sad, angry, annoyed, frustrated, scared, or anxious.",
  "Your mental health is a priority. Your happiness is essential. Your self-care is a necessity.",
  "Recovery is not one and done. It is a lifelong journey that takes place one day, one step at a time.",
  "You are not alone in this journey.",
  "Self-care is how you take your power back.",
  "It's okay to not have it all figured out. That's how you grow.",
  "Be proud of yourself for how hard you're trying.",
  "Your present circumstances don't determine where you can go; they merely determine where you start."
];

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const quoteOpacity = useRef(new Animated.Value(0)).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // We'll keep track of screen dimensions for future responsive features
  useEffect(() => {
    // Handle any future dimension changes if needed
    const subscription = Dimensions.addEventListener('change', () => {
      // Future responsive layout adjustments can be added here
    });

    return () => subscription?.remove();
  }, []);

  // Check if user has completed personal information
  useEffect(() => {
    console.log('Dashboard userData:', userData);

    // If user is not verified, redirect to email verification
    if (!userData.emailVerified) {
      console.log('User not verified, redirecting to email verification');
      router.replace('/email-verification');
      return; // Exit early
    }

    // If user is verified but has no personal info and hasn't completed profile, redirect to personal information
    if (userData.emailVerified && !userData.hasCompletedProfile) {
      console.log('User verified but profile not complete, redirecting to personal information');
      router.replace('/personal-information');
      return; // Exit early
    }

    // We've removed the back handler code to fix the error

    // Start animations when component mounts
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(quoteOpacity, {
        toValue: 1,
        duration: 1500,
        delay: 500,
        useNativeDriver: true,
      })
    ]).start();

    // Set a random quote
    const randomIndex = Math.floor(Math.random() * inspirationalQuotes.length);
    setQuote(inspirationalQuotes[randomIndex]);

    // Cleanup function - removed BackHandler code
    return () => {};
  }, [userData.firstName]);

  const [quote, setQuote] = useState(inspirationalQuotes[0]);
  const moodOptions = ['😊', '🙂', '😐', '😔', '😫'];
  const moodLabels = ['Great', 'Good', 'Okay', 'Bad', 'Awful'];
  const [selectedMood, setSelectedMood] = useState(null);

  const handleMoodSelect = (mood, index) => {
    setSelectedMood(index);
    // Navigate to mood journal with the selected mood
    router.push({
      pathname: '/mood-journal',
      params: {
        mood: mood,
        moodIndex: index
      }
    });
  };

  const handleProfilePress = () => {
    router.push('/user-profile');
  };

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="#6B9142" barStyle="light-content" />

      <SafeAreaView style={styles.safeArea}>
        {/* Header with Logo */}
        <View style={styles.headerSimple}>
          {/* Mentalease Logo */}
          <View style={styles.logoContainer}>
            <Image
              source={require('../assets/logo mentalease.png')}
              style={styles.headerLogo}
              resizeMode="contain"
            />
            <Text style={styles.logoText}>Mentalease</Text>
          </View>

          <View style={styles.userInfoContainer}>
            <TouchableOpacity
              style={styles.profilePicture}
              onPress={handleProfilePress}
              activeOpacity={0.7}
            >
              {userData.profileImage ? (
                <Image source={userData.profileImage} style={styles.profileImage} />
              ) : (
                <Text style={styles.profileInitial}>{(userData.firstName || 'U').charAt(0)}</Text>
              )}
            </TouchableOpacity>
            <View style={styles.userInfo}>
              <View style={styles.userNameContainer}>
                <Text style={styles.userName}>
                  Hi, {userData.hidePersonalInfo ? '••••••' : (userData.firstName || 'User')}!
                </Text>
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={toggleHidePersonalInfo}
                >
                  <Text style={styles.eyeIcon}>{userData.hidePersonalInfo ? '👁️' : '👁️‍🗨️'}</Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.userControlNumber}>ID: {userData.controlNumber || 'New User'}</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.calendarButton}
            onPress={() => router.replace('/mood-tracker')}
            activeOpacity={0.7}
          >
            <Text style={styles.calendarDateIcon}>17</Text>
          </TouchableOpacity>
        </View>

        {/* Inspirational Quote */}
        <Animated.View
          style={[
            styles.quoteContainer,
            { opacity: quoteOpacity }
          ]}
        >
          <Text style={styles.quoteText}>"{quote}"</Text>
        </Animated.View>

        <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {/* Mood Tracker Card with Animation */}
          <Animated.View
            style={[
              styles.moodTrackerCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <View style={styles.cardGradient}>
              <Text style={styles.cardTitle}>How are you feeling today?</Text>
              <View style={styles.moodOptions}>
                {moodOptions.map((mood, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.moodOption,
                      selectedMood === index && styles.selectedMoodOption
                    ]}
                    onPress={() => handleMoodSelect(mood, index)}
                  >
                    <View style={[
                      styles.moodEmojiContainer,
                      selectedMood === index && styles.selectedMoodEmojiContainer
                    ]}>
                      <Text style={styles.moodEmoji}>{mood}</Text>
                    </View>
                    <Text style={[
                      styles.moodLabel,
                      selectedMood === index && styles.selectedMoodLabel
                    ]}>
                      {moodLabels[index]}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </Animated.View>

          {/* AI Chatbot Card */}
          <Animated.View
            style={[
              styles.supportCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <View style={styles.pinkCardBackground}>
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>🤖</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.supportTitle}>Feeling overwhelmed or need someone to talk to?</Text>
              <Text style={styles.supportDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7.
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonIcon}>💬</Text>
                <Text style={styles.chatButtonText}>Chat with AIRA</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Stress Management Card */}
          <Animated.View
            style={[
              styles.stressCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#E8F3D8', '#D8EAC0']}
              style={styles.cardGradient}
            >
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>🧘</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>Stress Management</Text>
              </View>
              <Text style={styles.supportDescription}>
                Track your thoughts, feelings, and activities to identify patterns and improve your mental well-being.
              </Text>
              <TouchableOpacity
                style={styles.startButton}
                onPress={() => router.push('/mood-journal')}
              >
                <Text style={styles.startButtonIcon}>📝</Text>
                <Text style={styles.startButtonText}>Start Journaling</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Assessment Card */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#E0F0FF', '#C8E4FF']}
              style={styles.cardGradient}
            >
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>🧠</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>Mental Health Assessment</Text>
              </View>
              <Text style={styles.supportDescription}>
                Take a quick assessment to better understand your current mental health state and get personalized recommendations.
              </Text>
              <TouchableOpacity
                style={styles.assessmentButton}
                onPress={() => router.push('/mental-assessment')}
              >
                <Text style={styles.assessmentButtonIcon}>📋</Text>
                <Text style={styles.assessmentButtonText}>Take Assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Appointments Card */}
          <Animated.View
            style={[
              styles.appointmentCard,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#FFE8D6', '#FFDAC1']}
              style={styles.cardGradient}
            >
              <View style={styles.cardHeader}>
                <View style={styles.cardIconContainer}>
                  <Text style={styles.cardIcon}>📅</Text>
                </View>
                <Text style={styles.cardHeaderTitle}>Online Consultation</Text>
              </View>
              <Text style={styles.supportDescription}>
                Schedule appointments with mental health professionals for personalized guidance and support.
              </Text>
              <TouchableOpacity
                style={styles.consultButton}
                onPress={() => router.push('/appointments')}
              >
                <Text style={styles.consultButtonIcon}>🗓️</Text>
                <Text style={styles.consultButtonText}>View Appointments</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>
        </ScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  safeArea: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight,
  },
  headerSimple: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : StatusBar.currentHeight + 15,
    paddingBottom: 15,
    backgroundColor: '#6B9142',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginRight: 12,
  },
  headerLogo: {
    width: 32,
    height: 32,
    marginBottom: 2,
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  quoteContainer: {
    marginTop: 16,
    marginHorizontal: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F5F9EE',
    borderRadius: 16,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
    width: '92%',
    alignSelf: 'center',
    borderWidth: 0,
  },
  quoteText: {
    color: '#333333',
    fontStyle: 'italic',
    textAlign: 'center',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500',
    flexWrap: 'wrap',
  },
  cardGradient: {
    borderRadius: 15,
    padding: 16,
    overflow: 'hidden',
    backgroundColor: '#F5F9EE',
  },
  pinkCardBackground: {
    borderRadius: 15,
    padding: 16,
    overflow: 'hidden',
    backgroundColor: '#FFE8E8',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 1,
  },
  cardIcon: {
    fontSize: 16,
  },
  cardHeaderTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  supportDescription: {
    fontSize: 13,
    color: '#666666',
    marginBottom: 12,
    lineHeight: 18,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#5A7D38',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  profileImage: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
  },
  profileInitial: {
    fontSize: 22,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userInfo: {
    justifyContent: 'center',
    flex: 1,
  },
  userNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  eyeButton: {
    padding: 5,
  },
  eyeIcon: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  userControlNumber: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  calendarButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#FF6B6B',
  },
  calendarIcon: {
    fontSize: 16,
  },
  // Date display removed as requested
  scrollContent: {
    padding: 12,
    paddingBottom: 70, // Space for bottom nav
  },
  moodTrackerCard: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    borderWidth: 0,
  },
  cardContent: {
    padding: 12,
  },
  cardTitle: {
    fontSize: 16,
    color: '#444444',
    marginBottom: 8,
    fontWeight: '600',
    textAlign: 'center',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  moodOption: {
    width: 50,
    alignItems: 'center',
  },
  selectedMoodOption: {
    transform: [{ scale: 1.05 }],
  },
  moodEmojiContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
    marginBottom: 6,
  },
  selectedMoodEmojiContainer: {
    backgroundColor: '#6B9142',
  },
  moodEmoji: {
    fontSize: 22,
  },
  moodLabel: {
    fontSize: 11,
    color: '#666666',
    textAlign: 'center',
    fontWeight: '500',
  },
  selectedMoodLabel: {
    color: '#6B9142',
    fontWeight: 'bold',
  },
  calendarButton: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  calendarDateIcon: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B9142',
  },
  supportCard: {
    borderRadius: 15,
    marginBottom: 16,
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  supportTitle: {
    fontSize: 15,
    color: '#444444',
    marginBottom: 6,
    fontWeight: '600',
  },
  chatButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatButtonIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 13,
    fontWeight: '600',
  },
  stressCard: {
    borderRadius: 15,
    marginBottom: 16,
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  startButton: {
    backgroundColor: '#6B9142',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
    shadowColor: '#6B9142',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  startButtonIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 13,
    fontWeight: '600',
  },
  assessmentCard: {
    borderRadius: 15,
    marginBottom: 16,
    shadowColor: '#4A90E2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  assessmentButton: {
    backgroundColor: '#4A90E2',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
    shadowColor: '#4A90E2',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  assessmentButtonIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  assessmentButtonText: {
    color: '#FFFFFF',
    fontSize: 13,
    fontWeight: '600',
  },
  appointmentCard: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#9C27B0',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  consultButton: {
    backgroundColor: '#9C27B0',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#9C27B0',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  consultButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  consultButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  spacer: {
    height: 25,
  },
  supportCardHelp: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#FF9800',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  helpButton: {
    backgroundColor: '#FF9800',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#FF9800',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  helpButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  paymentCard: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#00BCD4',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  paymentButton: {
    backgroundColor: '#00BCD4',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#00BCD4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  paymentButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  inquiriesCard: {
    borderRadius: 25,
    marginBottom: 25,
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
    overflow: 'hidden',
  },
  inquiryButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignSelf: 'flex-start',
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  inquiryButtonIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  inquiryButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  // Additional styles for bottom padding
  safeArea: {
    flex: 1,
  },
  statusBarBackground: {
    height: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight,
    backgroundColor: '#6B9142',
  },
});
