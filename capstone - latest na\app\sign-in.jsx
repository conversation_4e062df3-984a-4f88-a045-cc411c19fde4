import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  TouchableWithoutFeedback,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useUser } from './context/UserContext';
import { signIn } from './lib/auth-service';

const SignIn = () => {
  const router = useRouter();
  const { updateUserData } = useUser();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Handle back button press - simplified to avoid BackHandler errors
  useEffect(() => {
    // We've removed the BackHandler code to fix the error

    // Instead, we'll just add a simple back button in the UI
    // that navigates to the welcome screen
  }, [router]);

  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);

    try {
      console.log('Attempting to sign in with Supabase...');

      // Use our Supabase auth service
      const { data, error } = await signIn(email, password);

      if (error) {
        throw error;
      }

      console.log('Sign in successful:', data);

      // Get the user data from the database response
      const user = data.user;

      // Check if user has personal information but has_completed_profile is false
      const hasPersonalInfo = user.first_name && user.last_name && (user.age !== null && user.age !== undefined) && user.gender && user.phone && user.address;
      const shouldUpdateProfileFlag = hasPersonalInfo && !user.has_completed_profile;

      console.log('🔍 Profile check:', {
        first_name: !!user.first_name,
        last_name: !!user.last_name,
        age: user.age,
        age_valid: (user.age !== null && user.age !== undefined),
        gender: !!user.gender,
        phone: !!user.phone,
        address: !!user.address,
        hasPersonalInfo,
        current_flag: user.has_completed_profile,
        shouldUpdate: shouldUpdateProfileFlag
      });

      if (shouldUpdateProfileFlag) {
        console.log('🔄 User has personal info but profile flag is false. Updating database...');

        try {
          // Update the has_completed_profile flag in the database
          const updateResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/rest/v1/user?email=eq.${user.email}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
              'Prefer': 'return=representation'
            },
            body: JSON.stringify({
              has_completed_profile: true
            }),
          });

          if (updateResponse.ok) {
            console.log('✅ Profile completion flag updated in database');
            user.has_completed_profile = true; // Update local user object
          } else {
            console.log('❌ Failed to update profile completion flag');
          }
        } catch (error) {
          console.error('Error updating profile flag:', error);
        }
      }

      // Update user context with actual database values
      updateUserData({
        id: user.id || 'user_' + Math.random().toString(36).substring(2),
        email: user.email || email,
        emailVerified: user.email_verified || false,
        hasCompletedProfile: user.has_completed_profile || false,
        // Include any existing personal information
        firstName: user.first_name || "",
        middleName: user.middle_name || "",
        lastName: user.last_name || "",
        age: user.age || 0,
        gender: user.gender || "",
        civilStatus: user.civil_status || "",
        birthdate: user.birthdate || "",
        birthplace: user.birthplace || "",
        religion: user.religion || "",
        address: user.address || "",
        phone: user.phone || "",
        controlNumber: user.control_number || ""
      }, () => {
        console.log("User signed in successfully");

        // Let the dashboard handle the routing based on profile completion
        router.replace('/dashboard');
      });
    } catch (error) {
      console.error('Error signing in:', error.message);
      Alert.alert("Sign In Failed", error.message || "There was an error signing in. Please check your credentials and try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.push('/')}
            >
              <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.logoContainer}>
            {/* Mentalease Logo */}
            <View style={styles.logoPlaceholder}>
              <Image
                source={require('../assets/logo mentalease.png')}
                style={styles.logoImage}
                resizeMode="contain"
              />
            </View>
            <Text style={styles.logoText}>Mentalease</Text>
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.title}>Sign In</Text>
            <Text style={styles.subtitle}>
              Please enter your credentials to sign in to your account
            </Text>

            <Text style={styles.label}>Email Address</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email address"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              returnKeyType="next"
            />

            <Text style={styles.label}>Password</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
            />

            <TouchableOpacity
              style={[styles.signInButton, isLoading && styles.signInButtonDisabled]}
              onPress={() => {
                Keyboard.dismiss();
                handleSignIn();
              }}
              disabled={isLoading}
            >
              <Text style={styles.signInButtonText}>
                {isLoading ? "Signing in..." : "Sign in"}
              </Text>
            </TouchableOpacity>

            <Text style={styles.hintText}>
              Enter your Supabase email and password to sign in.
            </Text>
          </View>
        </SafeAreaView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

export default SignIn;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginBottom: 8,
  },
  logoImage: {
    width: 80,
    height: 80,
  },
  logoText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: 'bold',
  },
  formContainer: {
    backgroundColor: '#F5F9EE',
    borderRadius: 15,
    margin: 20,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B9142',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  label: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 20,
    fontSize: 16,
  },
  signInButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  signInButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  signInButtonDisabled: {
    backgroundColor: '#A9C795',
    opacity: 0.7,
  },
  hintText: {
    marginTop: 15,
    fontSize: 12,
    color: '#888888',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  testButton: {
    marginTop: 20,
    backgroundColor: '#888888',
    borderRadius: 30,
    paddingVertical: 10,
    alignItems: 'center',
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});




